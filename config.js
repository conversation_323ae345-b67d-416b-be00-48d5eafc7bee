// 配置文件 - 敏感信息应该在服务器端处理
// 这个文件只包含前端需要的非敏感配置信息

window.APP_CONFIG = {
    // API配置
    API: {
        BASE_URL: '/api',
        TIMEOUT: 10000,
        ENDPOINTS: {
            AUTH_CALLBACK: '/auth/callback'
        }
    },
    
    // 重定向配置
    REDIRECT: {
        BASE_URL: window.location.origin,
        // 允许的重定向域名白名单
        ALLOWED_DOMAINS: [
            window.location.hostname,
            '315xfb.cn',
            'wiki.ns.315xfb.cn'
        ]
    },
    
    // UI配置
    UI: {
        LOADING_TEXT: '正在登录中，请稍候...',
        ERROR_MESSAGES: {
            INVALID_CODE: '未找到有效的授权码，请重新授权',
            LOGIN_FAILED: '登录失败，请稍后重试',
            TIMEOUT: '请求超时，请检查网络连接',
            UNAUTHORIZED: '授权失败，请重新授权',
            SERVER_ERROR: '服务器错误，请联系管理员',
            REDIRECT_ERROR: '重定向失败，请联系管理员'
        }
    }
};
