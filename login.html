<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>登录中</title>
    <meta name="viewport" content="user-scalable=no, width=device-width, initial-scale=1.0"/>
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    <script src="config.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            margin: 0;
            background-color: #f5f5f5;
        }
        .loading {
            text-align: center;
        }
        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #3498db;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 2s linear infinite;
            margin: 0 auto 20px;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .error {
            color: #e74c3c;
            margin-top: 20px;
        }
        .hidden {
            display: none;
        }
    </style>
</head>
<body>
    <div class="loading">
        <div class="spinner"></div>
        <p>正在登录中，请稍候...</p>
        <div id="error-message" class="error hidden"></div>
    </div>

<script>
    // 使用外部配置文件
    const CONFIG = window.APP_CONFIG;

    // 显示错误信息
    function showError(message) {
        const errorDiv = document.getElementById('error-message');
        errorDiv.textContent = message;
        errorDiv.classList.remove('hidden');
    }

    // 验证code参数
    function validateCode(code) {
        return code && typeof code === 'string' && code.length > 0;
    }

    // 安全的重定向函数
    function safeRedirect(url) {
        try {
            const redirectUrl = new URL(url);
            // 检查是否为允许的域名
            const isAllowedDomain = CONFIG.REDIRECT.ALLOWED_DOMAINS.some(domain =>
                redirectUrl.hostname === domain || redirectUrl.hostname.endsWith('.' + domain)
            );

            if (isAllowedDomain) {
                window.location.replace(url);
            } else {
                throw new Error('不安全的重定向目标');
            }
        } catch (error) {
            console.error('重定向错误:', error);
            showError(CONFIG.UI.ERROR_MESSAGES.REDIRECT_ERROR);
        }
    }

    // 主要逻辑
    function handleLogin() {
        // 获取回调URL中的code参数
        const urlParams = new URLSearchParams(window.location.search);
        const code = urlParams.get('code');

        if (!validateCode(code)) {
            showError(CONFIG.UI.ERROR_MESSAGES.INVALID_CODE);
            return;
        }

        // 向后端发送请求处理授权码
        $.ajax({
            type: 'POST', // 使用POST更安全
            url: CONFIG.API.BASE_URL + CONFIG.API.ENDPOINTS.AUTH_CALLBACK,
            data: JSON.stringify({ code: code }),
            headers: {
                "Content-Type": "application/json",
                // 添加CSRF保护头部（如果需要）
                "X-Requested-With": "XMLHttpRequest"
            },
            dataType: 'json',
            timeout: CONFIG.API.TIMEOUT,
            success: function (response) {
                if (response && response.success && response.user && response.user.id) {
                    // 构建安全的重定向URL
                    const redirectUrl = `${CONFIG.REDIRECT.BASE_URL}/user/index?id=${encodeURIComponent(response.user.id)}`;
                    safeRedirect(redirectUrl);
                } else {
                    showError(CONFIG.UI.ERROR_MESSAGES.LOGIN_FAILED + '：' + (response.message || '未知错误'));
                }
            },
            error: function (jqXHR, textStatus, errorThrown) {
                console.error('登录请求失败:', textStatus, errorThrown);
                let errorMessage = CONFIG.UI.ERROR_MESSAGES.LOGIN_FAILED;

                if (textStatus === 'timeout') {
                    errorMessage = CONFIG.UI.ERROR_MESSAGES.TIMEOUT;
                } else if (jqXHR.status === 401) {
                    errorMessage = CONFIG.UI.ERROR_MESSAGES.UNAUTHORIZED;
                } else if (jqXHR.status === 500) {
                    errorMessage = CONFIG.UI.ERROR_MESSAGES.SERVER_ERROR;
                }

                showError(errorMessage);
            }
        });
    }

    // 页面加载完成后执行
    $(document).ready(function() {
        handleLogin();
    });
</script>
</body>
</html>
